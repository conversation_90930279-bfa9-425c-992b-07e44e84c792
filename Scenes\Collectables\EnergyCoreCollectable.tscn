[gd_scene load_steps=4 format=3 uid="uid://c1lsn0rglq41p"]

[ext_resource type="Script" path="res://Scripts/Collectable.gd" id="1_collectable"]
[ext_resource type="Texture2D" uid="uid://mnnahy7d43xe" path="res://Assets/kenney_simple-space/PNG/Default/effect_yellow.png" id="2_energy"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 98.6154

[node name="EnergyCoreCollectable" type="Area2D"]
script = ExtResource("1_collectable")
collectable_type = 3
point_value = 100
orbit_radius = 250.0
orbit_speed = 0.4
collection_name = "Energy Core"

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 1, 0.5, 1)
texture = ExtResource("2_energy")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="CollectionParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 30
lifetime = 1.5
explosiveness = 1.0
direction = Vector2(0, -1)
gravity = Vector2(0, 40)
initial_velocity_min = 80.0
initial_velocity_max = 160.0
scale_amount_min = 0.6
scale_amount_max = 2.0
color = Color(0, 1, 0, 1)
