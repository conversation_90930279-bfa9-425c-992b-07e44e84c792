[gd_scene load_steps=18 format=3 uid="uid://c0pbif8cyy7x7"]

[ext_resource type="PackedScene" uid="uid://cvy6qvmeierso" path="res://Scenes/planet_large.tscn" id="1_j5yw3"]
[ext_resource type="Texture2D" uid="uid://u0nthwjjti6c" path="res://Assets/StarBackgroundTile.png" id="2_2irst"]
[ext_resource type="PackedScene" uid="uid://vuorl2ga7vdk" path="res://Scenes/planet_medium.tscn" id="2_kldst"]
[ext_resource type="Script" uid="uid://dx7wa3d4fr6xu" path="res://Scripts/star_background.gd" id="3_2irst"]
[ext_resource type="PackedScene" uid="uid://d1rrwaam8e5cr" path="res://Scenes/planet_small.tscn" id="3_b2bpf"]
[ext_resource type="Shader" uid="uid://eypjeee8ergs" path="res://Assets/Shaders/StarBackground2.gdshader" id="3_wxwew"]
[ext_resource type="PackedScene" uid="uid://big3rsi88wl76" path="res://Scenes/Player.tscn" id="4_kldst"]
[ext_resource type="PackedScene" uid="uid://14epulnfjodl" path="res://Scenes/Home.tscn" id="5_b2bpf"]
[ext_resource type="PackedScene" uid="uid://p4aoyegbc6gj" path="res://Scenes/UI/GameHUD.tscn" id="8_hud"]
[ext_resource type="Script" uid="uid://dfjdljl46khuv" path="res://Scripts/GameController.gd" id="9_controller"]
[ext_resource type="PackedScene" uid="uid://c8x7y2qm8qrst" path="res://Scenes/Collectables/StarCollectable.tscn" id="10_star"]
[ext_resource type="PackedScene" uid="uid://bpykgsig0p7c4" path="res://Scenes/Collectables/SatelliteCollectable.tscn" id="11_satellite"]
[ext_resource type="PackedScene" uid="uid://dkjlxh0onjytk" path="res://Scenes/Collectables/CrystalCollectable.tscn" id="12_crystal"]
[ext_resource type="PackedScene" uid="uid://c1lsn0rglq41p" path="res://Scenes/Collectables/EnergyCoreCollectable.tscn" id="13_energy"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_acvyw"]
shader = ExtResource("3_wxwew")

[sub_resource type="Shader" id="Shader_fgofq"]
code = "shader_type canvas_item;

uniform float paralax = 0.001;
uniform vec2 offset = vec2(0,0);


void vertex() {
	// Called for every vertex the material is visible on.
	UV += offset * paralax;
}"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_fgofq"]
shader = SubResource("Shader_fgofq")
shader_parameter/paralax = 1.0
shader_parameter/offset = Vector2(0, 0)

[node name="Game" type="Node2D"]
script = ExtResource("9_controller")

[node name="HUDLayer" type="CanvasLayer" parent="."]
layer = 10

[node name="GameHUD" parent="HUDLayer" instance=ExtResource("8_hud")]

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = -1

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
z_index = -1
z_as_relative = false
material = SubResource("ShaderMaterial_acvyw")
offset_left = -619.0
offset_top = -324.0
offset_right = 2811.0
offset_bottom = 1626.0
color = Color(0, 0, 0, 1)

[node name="TextureRect" type="TextureRect" parent="CanvasLayer/ColorRect"]
visible = false
z_index = -1
z_as_relative = false
material = SubResource("ShaderMaterial_fgofq")
layout_mode = 0
offset_right = 3445.0
offset_bottom = 1985.0
texture = ExtResource("2_2irst")
stretch_mode = 1
script = ExtResource("3_2irst")

[node name="Planet" parent="." instance=ExtResource("1_j5yw3")]
position = Vector2(-1319, 568)
gravityCurve = null

[node name="Planet2" parent="." instance=ExtResource("2_kldst")]
position = Vector2(595, -454)
gravityCurve = null

[node name="Planet3" parent="." instance=ExtResource("3_b2bpf")]
position = Vector2(261, -765)
gravityCurve = null

[node name="Player" parent="." instance=ExtResource("4_kldst")]
position = Vector2(695, -24)
launch_power = 5.0

[node name="Planet4" parent="." instance=ExtResource("5_b2bpf")]
position = Vector2(659, -1086)
gravityCurve = null

[node name="Planet5" parent="." instance=ExtResource("1_j5yw3")]
position = Vector2(-3226, 216)
gravityCurve = null

[node name="Planet6" parent="." instance=ExtResource("5_b2bpf")]
position = Vector2(-3022, 6248)
gravityCurve = null

[node name="Planet7" parent="." instance=ExtResource("2_kldst")]
position = Vector2(2252, 5014)
gravityCurve = null

[node name="Planet8" parent="." instance=ExtResource("1_j5yw3")]
position = Vector2(-6838, 3592)
gravityCurve = null

[node name="Planet9" parent="." instance=ExtResource("3_b2bpf")]
position = Vector2(-6426, -260)
gravityCurve = null

[node name="Planet10" parent="." instance=ExtResource("3_b2bpf")]
position = Vector2(-1900, 3592)
gravityCurve = null

[node name="Planet11" parent="." instance=ExtResource("1_j5yw3")]
position = Vector2(-3790, 5119)
gravityCurve = null

[node name="Planet12" parent="." instance=ExtResource("2_kldst")]
position = Vector2(-4396, 1953)
gravityCurve = null

[node name="Planet13" parent="." instance=ExtResource("3_b2bpf")]
position = Vector2(1858, 2858)
gravityCurve = null

[node name="Planet14" parent="." instance=ExtResource("2_kldst")]
position = Vector2(4738, 2858)
gravityCurve = null

[node name="Planet15" parent="." instance=ExtResource("3_b2bpf")]
position = Vector2(-2462, -2367)
gravityCurve = null

[node name="Planet16" parent="." instance=ExtResource("3_b2bpf")]
position = Vector2(3421, -2573)
gravityCurve = null

[node name="Planet17" parent="." instance=ExtResource("1_j5yw3")]
position = Vector2(706, -4260)
gravityCurve = null

[node name="StarCollectable2" parent="." instance=ExtResource("10_star")]
position = Vector2(261, -765)
orbit_radius = 150.0

[node name="StarCollectable3" parent="." instance=ExtResource("10_star")]
position = Vector2(-1319, 568)
orbit_radius = 200.0

[node name="StarCollectable4" parent="." instance=ExtResource("10_star")]
position = Vector2(1858, 2858)
orbit_radius = 250.0

[node name="StarCollectable5" parent="." instance=ExtResource("10_star")]
position = Vector2(-2462, -2367)
orbit_radius = 250.0

[node name="SatelliteCollectable1" parent="." instance=ExtResource("11_satellite")]
position = Vector2(2252, 5014)
orbit_radius = 250.0

[node name="SatelliteCollectable2" parent="." instance=ExtResource("11_satellite")]
position = Vector2(-3226, 216)
orbit_radius = 270.0

[node name="SatelliteCollectable3" parent="." instance=ExtResource("11_satellite")]
position = Vector2(4738, 2858)
orbit_radius = 280.0

[node name="CrystalCollectable1" parent="." instance=ExtResource("12_crystal")]
position = Vector2(-6838, 3592)
orbit_radius = 250.0

[node name="CrystalCollectable2" parent="." instance=ExtResource("12_crystal")]
position = Vector2(706, -4260)
orbit_radius = 270.0

[node name="EnergyCoreCollectable1" parent="." instance=ExtResource("13_energy")]
position = Vector2(-4396, 1953)
orbit_radius = 270.0

[node name="HUDLayer2" type="CanvasLayer" parent="."]
layer = 10
